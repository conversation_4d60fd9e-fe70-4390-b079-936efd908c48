import { ScrollingModule } from "@angular/cdk/scrolling";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { CoreCommonModule } from "@core/common.module";
import { CoreCardModule } from "@core/components/core-card/core-card.module";
import { CoreTouchspinModule } from "@core/components/core-touchspin/core-touchspin.module";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { NgSelectModule } from "@ng-select/ng-select";
import { NgxDatatableModule } from "@swimlane/ngx-datatable";
import { PipeModule } from "app/layout/components/pipe/pipe.module";
import { ViewDetailFileModule } from "app/layout/components/view-detail-file/view-detail-file.module";
import { Ng2FlatpickrModule } from "ng2-flatpickr";
import { AutoCompleteModule } from "primeng/autocomplete";
import { TimKiemThongMinhComponent } from "./tim-kiem-thong-minh.component";

// const route: Routes = [
//   {
//     path: "",
//     component: TimKiemThongMinhComponent,
//   },
// ];
@NgModule({
  declarations: [TimKiemThongMinhComponent],
  imports: [
    CommonModule,
    PipeModule,
    Ng2FlatpickrModule,
    NgSelectModule,
    CoreCommonModule,
    // RouterModule.forChild(route),
    ReactiveFormsModule,
    FormsModule,
    NgbModule,
    NgxDatatableModule,
    ViewDetailFileModule,
    CoreCardModule,
    AutoCompleteModule,
    CoreTouchspinModule,
    ScrollingModule,
  ],
  exports: [TimKiemThongMinhComponent],
})
export class TimKiemThongMinhModule {}
