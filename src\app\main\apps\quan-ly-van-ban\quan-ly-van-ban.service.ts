import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Params } from "@angular/router";
import { environment } from "environments/environment";
import { BehaviorSubject, Observable } from "rxjs";
import { InterceptorSkipHeader } from "@core/components/loading/loading.interceptor";

@Injectable({
  providedIn: "root",
})
export class QuanLyVanBanService {
  public isShowTourGuide: BehaviorSubject<boolean> =
    new BehaviorSubject<boolean>(false);
  constructor(private http: HttpClient) {}
  getAllFileOcrNoPage(keyword) {
    const params: Params = {
      page: 1,
      keyword: keyword,
      compare: 1,
    };
    return this.http.get<any>(`${environment.apiUrl}/ocr/documents`, {
      params,
    });
  }
  compareFile(body) {
    return this.http.post<any>(`${environment.apiUrl}/document/compare`, body);
  }
  addFile(body) {
    return this.http.post<any>(`${environment.apiUrl}/ocr/documents`, body);
  }

  addFileBySearch(body) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.post<any>(
      `${environment.apiUrl}/ocr/documents/add_search_document`,
      body,
      { headers }
    );
  }

  downloadFile(docId: number, fileType: string): Observable<Blob> {
    return this.http.get(
      `${environment.apiUrl}/document/download?doc_id=${docId}&type=${fileType}`,
      {
        responseType: "blob", // Để nhận file nhị phân
      }
    );
  }
  saveFile(docId: number, fileType: string): void {
    this.downloadFile(docId, fileType).subscribe(
      (blob) => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `document.${fileType}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      },
      (error) => {
        console.error("Lỗi khi tải file:", error);
        alert("Tải file thất bại!");
      }
    );
  }
  getAllWorkSpace(search, page, size) {
    const params = {
      search: search,
      page: page,
      page_size: size,
    };
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.get<any>(`${environment.apiUrl}/workspace`, { params, headers });
  }
  addWorkSpace(body) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.post<any>(`${environment.apiUrl}/workspace/`, body, { headers });
  }
  updateWorkSpace(body, idWorkSpace) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.put<any>(
      `${environment.apiUrl}/workspace/${idWorkSpace}`,
      body,
      { headers }
    );
  }
  deleteWorkSpace(idWorkSpace) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.delete<any>(
      `${environment.apiUrl}/workspace/${idWorkSpace}`,
      { headers }
    );
  }
  informationExtract(document_id) {
    return this.http.post<any>(
      `${environment.apiUrl}/document/extract-document`,
      { document_id: document_id }
    );
  }
}
