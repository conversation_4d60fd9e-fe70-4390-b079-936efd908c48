import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { ChangeDataService } from "app/auth/service/change-data.service";
import { FormType } from "app/models/FormType";
import { ToastrService } from "ngx-toastr";
import { QuanLyVanBanService } from "../quan-ly-van-ban.service";

@Component({
  selector: "app-work-space-control",
  templateUrl: "./work-space-control.component.html",
  styleUrls: ["./work-space-control.component.scss"],
})
export class WorkSpaceControlComponent implements OnInit {
  @Input("modal") public modal: NgbActiveModal;
  @Input("title") public title: string;
  @Input("type") public type: number;
  @Input("row") public row: any;
  @Output() destroyed = new EventEmitter<void>();
  public formWorkSpace: FormGroup;
  public submitted: boolean = false;
  constructor(
    private fb: FormBuilder,
    private qlvb: QuanLyVanBanService,
    private changeData: ChangeDataService,
    private _toast: ToastrService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.formWorkSpace = this.fb.group({
      name: [null, [Validators.required, Validators.maxLength(100)]],
      description: [null],
    });
    if (this.type == FormType.Update) {
      this.formWorkSpace.patchValue({ ...this.row });
    }
  }
  get f() {
    return this.formWorkSpace.controls;
  }
  submit() {
    this.submitted = true;
    if (this.formWorkSpace.invalid) {
      return;
    }
    const formData = new FormData();
    Object.keys(this.formWorkSpace.value).forEach((key) => {
      formData.append(key, this.formWorkSpace.value[key]);
    });
    if (this.type == FormType.Create) {
      this.qlvb.addWorkSpace(formData).subscribe(
        (res) => {
          if (res) {
            // this.changeData.changeData.next(true);
            localStorage.setItem("isNewUser", JSON.stringify(false)); // dánh dấu là người dùng cũ
            this._toast.success("Thêm không gian dự án", "Thành công", {
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
              closeButton: true,
            });
            this.modal.close();
            this.router.navigate([`/quan-ly-van-ban/workspace/${res.id}`], {
              // tự động chueyern vào workspace đã tạo
              queryParams: { workSpaceName: res.name },
            });
          }
        },
        (error) => {
          this._toast.error("Thêm không gian dự án", "Thất bại", {
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
            closeButton: true,
          });
        }
      );
    } else {
      this.qlvb.updateWorkSpace(formData, this.row.id).subscribe(
        (res) => {
          if (res) {
            this.changeData.changeData.next(true);
            this._toast.success("Cập nhật không gian dự án", "Thành công", {
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
              closeButton: true,
            });
            this.modal.close();
          }
        },
        (error) => {
          this._toast.error("Cập nhật không gian dự án", "Thất bại", {
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
            closeButton: true,
          });
        }
      );
    }
  }
  ngOnDestroy(): void {
    // this.destroyed.emit(); // Gửi sự kiện khi bị destroy
  }
}
