import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { RouterModule, Routes } from "@angular/router";
import { CoreCommonModule } from "@core/common.module";
import { CoreDirectivesModule } from "@core/directives/directives";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { NgSelectModule } from "@ng-select/ng-select";
import { NgxDatatableModule } from "@swimlane/ngx-datatable";
import { ViewDetailFileModule } from "app/layout/components/view-detail-file/view-detail-file.module";
import { BocTachThongTinModule } from "../chuyen-doi-van-ban/boc-tach-thong-tin.module";
import { QuanLyVanBanComponent } from "./quan-ly-van-ban.component";
import { WorkSpaceControlComponent } from "./work-space-control/work-space-control.component";
import { TimKiemThongMinhModule } from "../tim-kiem-thong-minh/tim-kiem-thong-minh.module";
import { ChatbotModule } from "./detail-work-space/chatbot/chatbot.module";
import { SoSanhVanBanModule } from "../so-sanh-van-ban/so-sanh-van-ban.module";
import { DashboardModule } from "../dashboard/dashboard.module";
import { PipeModule } from "app/layout/components/pipe/pipe.module";
const routes: Routes = [
  {
    path: "",
    component: QuanLyVanBanComponent,
  },

  {
    path: "workspace",
    loadChildren: () =>
      import("./detail-work-space/detail-work-space.module").then(
        (m) => m.DetailWorkSpaceModule
      ),
  },
];
@NgModule({
  declarations: [WorkSpaceControlComponent, QuanLyVanBanComponent],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    NgxDatatableModule,
    CoreCommonModule,
    NgbModule,
    NgSelectModule,
    ViewDetailFileModule,
    ReactiveFormsModule,
    CoreDirectivesModule,
    BocTachThongTinModule,
    TimKiemThongMinhModule,
    ChatbotModule,
    SoSanhVanBanModule,
    DashboardModule,
    PipeModule,
  ],
})
export class QuanLyVanBanModule {}
