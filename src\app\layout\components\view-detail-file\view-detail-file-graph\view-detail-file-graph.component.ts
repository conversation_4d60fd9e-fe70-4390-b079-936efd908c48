import {
  Component,
  Input,
  OnInit,
  OnChanges,
  SimpleChanges,
  Output,
  EventEmitter,
  HostListener,
  ViewEncapsulation,
  ChangeDetectorRef,
  AfterViewInit,
  ChangeDetectionStrategy,
  Renderer2,
} from "@angular/core";
import { ColumnMode, SelectionType } from "@swimlane/ngx-datatable";
import { ActivatedRoute } from "@angular/router";
import { FlatpickrOptions } from "ng2-flatpickr";
import { ViewDetailFileService } from "../view-detail-file.service";
import { ToastrService } from "ngx-toastr";
import { DetailWorkSpaceService } from "app/main/apps/quan-ly-van-ban/detail-work-space/detail-work-space.service";
import {
  getNodeIdFromApiNode,
  safeArray,
  truncate,
  formatTooltipText,
  getVietnameseRelationshipLabel,
} from "./helper/helper";
import {
  GraphApiResponse,
  ApiNode,
  GraphFormState,
  GraphRequestBody,
  DocumentData,
  ContextMenuItem,
  DateFilterMode,
  GraphNode,
  GraphLink,
  DocumentListItem,
  DocumentTableRow,
} from "./types/graph.types";
import {
  D3GraphNode,
  D3GraphLink,
  GraphRendererCallbacks,
} from "./models/graph-renderer.models";
import {
  NodeFilterContext,
} from "./services/graph-filter.service";
import {
  NODE_SIZES,
  VALIDATION_LIMITS,
  TOAST_CONFIG,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  INFO_MESSAGES,
  TINH_TRANG_HIEU_LUC_OPTIONS,
  BO_LOC_LOAI_VAN_BAN_OPTIONS,
  CO_QUAN_BAN_HANH_OPTIONS,
  BO_LOC_MOI_QUAN_HE_OPTIONS,
  FilterOption,
} from "./constants/graph.constants";
// Services
import { GraphCacheService } from "./services/graph-cache.service";
import { GraphFormService } from "./services/graph-form.service";
import { GraphDataTransformerService } from "./services/graph-data-transformer.service";
import { GraphFilterService } from "./services/graph-filter.service";
import { GraphExpansionService } from "./services/graph-expansion.service";
import { DocumentListService } from "./services/document-list.service";
import { GraphRendererService } from "./services/graph-renderer.service";

@Component({
  selector: "app-view-detail-file-graph",
  templateUrl: "./view-detail-file-graph.component.html",
  styleUrls: [
    "./view-detail-file-graph.component.scss",
    "../../../../../@core/scss/angular/libs/noui-slider.component.scss",
  ],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GraphComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() graphData: GraphApiResponse | null = null;
  @Input() currentDocumentEsId: string = "";
  @Input() formState: GraphFormState | null = null;
  @Input() isLoadingGraphExternal: boolean = false;
  @Input() hiddenNodeIds: Set<string> = new Set();
  @Input() previousNodePositions: Map<string, { x: number; y: number }> = new Map();
  @Input() documentListExpanded: boolean = false;
  @Input() showDocumentTable: boolean = false;
  @Input() isTimKiemMode: boolean = false;
  @Output() documentClicked = new EventEmitter<DocumentData>();
  @Output() uiStateChange = new EventEmitter<{
    hiddenNodeIds?: Set<string>;
    previousNodePositions?: Map<string, { x: number; y: number }>;
    documentListExpanded?: boolean;
    showDocumentTable?: boolean;
  }>();

  nodes: GraphNode[] = [];
  links: GraphLink[] = [];
  private apiNodeMap: Map<string, ApiNode> = new Map();
  private rootNodeId: string = "";
  isLoadingGraph: boolean = false;
  
  // Data for graph visualization component
  d3Nodes: D3GraphNode[] = [];
  d3Links: D3GraphLink[] = [];
  
  graphCallbacks?: GraphRendererCallbacks;
  contextMenuVisible: boolean = false;
  contextMenuPosition = { x: 0, y: 0 };
  contextMenuItem: ContextMenuItem | null = null;
  showExpandSubmenu: boolean = false;

  // Modal state for conditional expansion
  showExpansionModal: boolean = false;
  modalFormState: GraphFormState | null = null;
  isModalMode: boolean = false;
  private savedContextMenuItem: ContextMenuItem | null = null;
  selectAllDocuments: boolean = false;
  documentList: DocumentListItem[] = [];
  selectedFiles: Array<DocumentData & { id: number }> = [];
  isFullTableView: boolean = false;
  fullTableSearch: string = "";
  documentListSearch: string = "";
  highlightedDocumentId: string | null = null;
  hoveredDocumentId: string | null = null;
  page: number = 1;
  pageSize: number = 8;
  isSavingFiles: boolean = false;
  activeDocumentTab: string = "timkiem"; // Active tab for document list
  sortColumn: string = "";
  sortDirection: "asc" | "desc" = "asc";
  ColumnMode = ColumnMode;
  isGraphError: boolean = false;
  SelectionType = SelectionType;
  selectedTableRows: DocumentTableRow[] = []; // Selected rows for ngx-datatable (must be same object references as rows)

  // Memoization cache for expensive operations
  private filteredDocumentsCache: DocumentTableRow[] | null = null;
  private filteredDocumentsCacheKey: string = "";
  private paginatedDocumentsCache: DocumentTableRow[] | null = null;
  private paginatedDocumentsCacheKey: string = "";

  constructor(
    private viewDetailFile: ViewDetailFileService,
    private toast: ToastrService,
    private route: ActivatedRoute,
    private workspace: DetailWorkSpaceService,
    private cdr: ChangeDetectorRef,
    // Injected services
    private cacheService: GraphCacheService,
    private formService: GraphFormService,
    private transformerService: GraphDataTransformerService,
    private filterService: GraphFilterService,
    private expansionService: GraphExpansionService,
    private documentListService: DocumentListService,
    private rendererService: GraphRendererService,
    private renderer: Renderer2
  ) {}

  dataFile: DocumentData = {} as DocumentData;
  private originalDocumentEsId: string = "";
  private currentNodeId: string = "";
  // Store seed node IDs for TimKiem mode (multiple document IDs from search)
  private timKiemSeedNodeIds: string[] = [];
  private documentTooltipEl: HTMLElement | null = null;
  showRightPanel: boolean = false; // Initially closed
  clauseContent: string = "";
  isLoadingClauseContent: boolean = false;
  hasClauseContentError: boolean = false;
  isClauseNode: boolean = false;

  typeDocument = "csdl"; // could be 'csdl', 'search', 'upload', or 'searching'

  customYearOptions: FlatpickrOptions = {
    altFormat: "d/m/Y",
    dateFormat: "Y-m-d",
    enableTime: false,
    altInput: true,
    mode: "range",
    minDate: "1900-01-01",
    maxDate: "2100-12-31",
  };

  depthSliderPips = {
    mode: "values",
    values: [1, 2, 3],
    density: 4,
    format: {
      to: (value: number) => value.toFixed(0),
      from: (value: string) => Number(value),
    },
  };

  /* -------------------------------
   * Static Options (from constants)
   * ----------------------------- */
  // Full options from constants (used in expansion modal - always show all options)
  fullTinhTrangHieuLucOptions: FilterOption[] = TINH_TRANG_HIEU_LUC_OPTIONS;
  fullBoLocLoaiVanBanOptions: FilterOption[] = BO_LOC_LOAI_VAN_BAN_OPTIONS;
  fullCoQuanBanHanhOptions: FilterOption[] = CO_QUAN_BAN_HANH_OPTIONS;
  fullBoLocMoiQuanHeOptions: FilterOption[] = BO_LOC_MOI_QUAN_HE_OPTIONS;

  // Filtered options (from backend, used in document-list-panel - only show available options)
  tinhTrangHieuLucOptions: FilterOption[] = TINH_TRANG_HIEU_LUC_OPTIONS;
  boLocLoaiVanBanOptions: FilterOption[] = BO_LOC_LOAI_VAN_BAN_OPTIONS;
  coQuanBanHanhOptions: FilterOption[] = CO_QUAN_BAN_HANH_OPTIONS;
  boLocMoiQuanHeOptions: FilterOption[] = BO_LOC_MOI_QUAN_HE_OPTIONS;

  ngOnInit(): void {
    // Store the original document ID before it gets overwritten
    this.originalDocumentEsId = this.currentDocumentEsId;
    // console.log(
    //   "currentDocumentEsId in graph component:",
    //   this.currentDocumentEsId
    // );

    // Initialize form state if not provided
    if (!this.formState) {
      this.formState = {
        search_legal_term: "VAN_BAN",
        isSearchAdvance: false,
        selectedBoLocMoiQuanHe: [],
        selectedTinhTrangHieuLuc: [],
        selectedBoLocLoaiVanBan: [],
        selectedCoQuanBanHanh: [],
        depth: 1,
        global_limit: 25,
        limit_per_seed: 25,
        dateFilterMode: null,
        dateFilterFrom: null,
        dateFilterTo: null,
        depthError: "",
        global_limitError: "",
        limit_per_seedError: "",
      };
    }

    // Ensure new filter fields exist when formState is provided from parent
    if (typeof this.formState.dateFilterMode === "undefined") {
      this.formState.dateFilterMode = null;
    }
    if (typeof this.formState.dateFilterFrom === "undefined") {
      this.formState.dateFilterFrom = null;
    }
    if (typeof this.formState.dateFilterTo === "undefined") {
      this.formState.dateFilterTo = null;
    }

    // Initialize UI state from @Input (preserved from parent)
    // Note: hiddenNodeIds and previousNodePositions are already set via @Input
    // documentListExpanded and showDocumentTable are also set via @Input

    // If graphData already present (e.g., pre-fetched), sync filter options and transform it
    if (this.graphData) {
      this.isGraphError = false;
      // In TimKiem mode, store seed node IDs from graphData for later use when view mode changes
      if (this.isTimKiemMode && this.graphData.seed_node_ids) {
        this.timKiemSeedNodeIds = safeArray(this.graphData.seed_node_ids).map(id => String(id));
      }
      this.updateFilterOptionsFromGraph();
      this.transformGraphData();
    }

    this.renderGraph();
  }

  /**
   * Update filter option lists from dynamic filter_options in graphData
   * Falls back to static constants when filter_options are missing.
   */
  private updateFilterOptionsFromGraph(): void {
    const filterOptions = (this.graphData as any)?.filter_options;
    if (!filterOptions) {
      return;
    }

    // Relationship types: intersect backend codes with our predefined label mapping
    if (Array.isArray(filterOptions.relationship_types)) {
      const available = new Set(filterOptions.relationship_types);
      this.boLocMoiQuanHeOptions = BO_LOC_MOI_QUAN_HE_OPTIONS.filter((opt) =>
        available.has(opt.value)
      );
    }

    // Issuing authorities
    if (Array.isArray(filterOptions.co_quan_ban_hanh)) {
      this.coQuanBanHanhOptions = filterOptions.co_quan_ban_hanh.map(
        (value: string) => ({ label: value, value })
      );
    }

    // Validity statuses
    if (Array.isArray(filterOptions.tinh_trang_hieu_luc)) {
      this.tinhTrangHieuLucOptions = filterOptions.tinh_trang_hieu_luc.map(
        (value: string) => ({ label: value, value })
      );
    }

    // Document types
    if (Array.isArray(filterOptions.loai_van_ban)) {
      this.boLocLoaiVanBanOptions = filterOptions.loai_van_ban.map(
        (value: string) => ({ label: value, value })
      );
    }
  }

  /**
   * Recalculate dynamic filter options from currently visible nodes.
   * Used after hiding/restoring nodes so filters only show values
   * that are still present in the visible graph.
   */
  private recalculateFilterOptionsFromVisibleGraph(): void {
    if (!this.nodes || this.nodes.length === 0) {
      this.coQuanBanHanhOptions = [];
      this.tinhTrangHieuLucOptions = [];
      this.boLocLoaiVanBanOptions = [];
      this.boLocMoiQuanHeOptions = [];
      return;
    }

    const authorities = new Set<string>();
    const statuses = new Set<string>();
    const types = new Set<string>();
    const relTypesInVisibleGraph = new Set<string>();

    const visibleNodeIds = new Set(this.nodes.map((n) => n.id));

    // Collect attribute-based filters from visible "Văn bản" nodes
    this.nodes.forEach((node) => {
      const apiNode = this.apiNodeMap.get(node.id);
      if (!apiNode || apiNode.nhan_ui !== "Văn bản") {
        return;
      }
      const attrs: any = apiNode.thuoc_tinh || {};
      const coQuanBanHanh = (attrs.co_quan_ban_hanh || "").trim();
      const tinhTrangHieuLuc = (attrs.tinh_trang_hieu_luc || "").trim();
      const loaiVanBan = (attrs.loai_van_ban || "").trim();

      if (coQuanBanHanh) authorities.add(coQuanBanHanh);
      if (tinhTrangHieuLuc) statuses.add(tinhTrangHieuLuc);
      if (loaiVanBan) types.add(loaiVanBan);
    });

    // Collect relationship types from links that connect only visible nodes
    this.links.forEach((link) => {
      const sourceId = String(link.source as string);
      const targetId = String(link.target as string);
      if (!visibleNodeIds.has(sourceId) || !visibleNodeIds.has(targetId)) {
        return;
      }
      const relType = (link as any).__relationshipType || (link as any).loai_moi_quan_he;
      if (relType) {
        relTypesInVisibleGraph.add(relType);
      }
    });

    this.coQuanBanHanhOptions = Array.from(authorities).map((value) => ({
      label: value,
      value,
    }));
    this.tinhTrangHieuLucOptions = Array.from(statuses).map((value) => ({
      label: value,
      value,
    }));
    this.boLocLoaiVanBanOptions = Array.from(types).map((value) => ({
      label: value,
      value,
    }));

    // For relationship types, keep labels from static mapping but only
    // show those that still exist in the visible graph
    if (relTypesInVisibleGraph.size > 0) {
      this.boLocMoiQuanHeOptions = BO_LOC_MOI_QUAN_HE_OPTIONS.filter((opt) =>
        relTypesInVisibleGraph.has(opt.value)
      );
    } else {
      this.boLocMoiQuanHeOptions = [];
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Update originalDocumentEsId if currentDocumentEsId changes and we don't have one yet
    if (
      changes["currentDocumentEsId"]?.currentValue &&
      !this.originalDocumentEsId
    ) {
      this.originalDocumentEsId = this.currentDocumentEsId;
    }

    // Handle UI state input changes (restored from parent when switching tabs)
    if (changes["hiddenNodeIds"]?.currentValue) {
      // hiddenNodeIds is already set via @Input, no action needed
    }
    if (changes["previousNodePositions"]?.currentValue) {
      // previousNodePositions is already set via @Input, no action needed
      // Re-render to apply preserved positions
      if (this.graphData) {
        this.renderGraph();
      }
    }
    if (changes["documentListExpanded"]?.currentValue !== undefined) {
      // documentListExpanded is already set via @Input, no action needed
    }
    if (changes["showDocumentTable"]?.currentValue !== undefined) {
      // showDocumentTable is already set via @Input, no action needed
    }

    if (changes["graphData"]?.currentValue) {
      // // console.log("Received graphData in graph component:", this.graphData);
      // Don't overwrite currentDocumentEsId to preserve the original document ID
      // Persist graphData to service to maintain state across tab switches
      this.viewDetailFile.graphData.next(this.graphData);
      
      // In TimKiem mode, store seed node IDs from graphData for later use when view mode changes
      if (this.isTimKiemMode && this.graphData?.seed_node_ids) {
        this.timKiemSeedNodeIds = safeArray(this.graphData.seed_node_ids).map(id => String(id));
      }
      
      // Update dynamic filter options from latest graph data
      this.updateFilterOptionsFromGraph();
      this.transformGraphData();
      // Trigger change detection for OnPush strategy
      this.cdr.markForCheck();
    }

  }

  ngAfterViewInit(): void {
    this.renderGraph();
  }

  /* -------------------------------
   * Form Logic
   * ----------------------------- */
  changeYear(event: any, isModal: boolean = false): void {
    const targetState = isModal ? this.modalFormState : this.formState;
    if (!targetState) return;
    this.formService.changeYear(event, targetState);

    // Only auto-apply the date filter for the main form (not modal)
    if (
      !isModal &&
      this.formState &&
      this.formState.dateFilterMode &&
      this.formState.dateFilterFrom !== null &&
      this.formState.dateFilterTo !== null
    ) {
      this.filterByDateRange();
    }
  }

  setDateFilterMode(mode: DateFilterMode | null, event?: Event): void {
    if (!this.formState) {
      return;
    }

    // Remember whether we had a fully-specified date range before this change
    const hadCompleteRangeBeforeToggle =
      !!this.formState.dateFilterMode &&
      this.formState.dateFilterFrom !== null &&
      this.formState.dateFilterTo !== null;

    // Toggle: if clicking the same mode, deactivate it
    if (this.formState.dateFilterMode === mode) {
      // Prevent default radio behavior to allow deselection
      if (event) {
        event.preventDefault();
        event.stopPropagation();
      }
      this.formState.dateFilterMode = null;

      // Manually uncheck all radio buttons in the group
      setTimeout(() => {
        const radioButtons = document.querySelectorAll(
          'input[name="date-filter-mode"]'
        ) as NodeListOf<HTMLInputElement>;
        radioButtons.forEach((radio) => {
          radio.checked = false;
        });
        // Force change detection to update the UI
        this.cdr.detectChanges();
      }, 0);

      // If a valid range was previously applied, re-render to clear that filter
      if (hadCompleteRangeBeforeToggle) {
        this.filterByDateRange();
      }
    } else {
      this.formState.dateFilterMode = mode;
      // Force change detection to update the UI
      this.cdr.detectChanges();

      // Only apply date filter when a mode is active AND both from/to years are set
      if (
        this.formState.dateFilterFrom !== null &&
        this.formState.dateFilterTo !== null
      ) {
        this.filterByDateRange();
      }
    }
  }


  validateDepth(event: any) {
    if (!this.formState) return;
    const value = Number(event.target.value);
    this.formService.validateDepth(this.formState, value);
  }

  validateGlobalLimit(event: any) {
    if (!this.formState) return;
    const value = Number(event.target.value);
    this.formService.validateGlobalLimit(this.formState, value);
  }

  validateLimitPerSeed(event: any) {
    if (!this.formState) return;
    const value = Number(event.target.value);
    this.formService.validateLimitPerSeed(this.formState, value);
  }

  onSubmitForm(): void {
    if (!this.formState) return;

    this.isGraphError = false;
    // Always use the original document ID for the API endpoint
    const targetId = this.originalDocumentEsId || this.currentDocumentEsId;

    // Validate that we have a document ID
    if (!targetId) {
      this.showErrorToast(ERROR_MESSAGES.NO_DOCUMENT_ID);
      return;
    }

    // Close document table when filters are applied
    this.showDocumentTable = false;
    this.emitUIStateChange({ showDocumentTable: false });

    // Build the request body using form service
    const body = this.formService.buildRequestBody(this.formState, targetId);

    // Make the API call
    this.isLoadingGraph = true;
    this.cdr.markForCheck(); // Update loading state immediately
    this.viewDetailFile.getDoThi(body).subscribe(
      (res) => {
        this.graphData = res;
        this.resetGraphState();
        this.viewDetailFile.graphData.next(this.graphData);
        this.transformGraphData();
        this.showSuccessToast(SUCCESS_MESSAGES.GRAPH_UPDATED);
        this.isGraphError = false;
        this.isLoadingGraph = false;
        this.cdr.markForCheck(); // Trigger change detection after data update
      },
      (error) => {
        this.handleApiError(error);
        this.isLoadingGraph = false;
        this.cdr.markForCheck(); // Trigger change detection after error
      }
    );
  }


  private renderGraph(): void {
    this.renderGraphWithD3();
  }

  /**
   * Prepare data for graph visualization component
   */
  private renderGraphWithD3(): void {
    if (!this.nodes.length) {
      this.d3Nodes = [];
      this.d3Links = [];
      this.previousNodePositions.clear();
      this.emitUIStateChange({ previousNodePositions: this.previousNodePositions });
      return;
    }

    // Calculate dynamic node sizes based on current connection count (degree)
    const degreeMap = new Map<string, number>();
    const addDegree = (id: string | number | undefined | null) => {
      if (id === undefined || id === null) return;
      const key = String(id);
      degreeMap.set(key, (degreeMap.get(key) || 0) + 1);
    };
    this.links.forEach((link) => {
      addDegree(link.source as string);
      addDegree(link.target as string);
    });

    // Get current node positions from renderer service before re-rendering
    // This preserves positions of existing nodes when graph is updated
    const currentPositions = this.rendererService.getCurrentNodePositions();
    if (currentPositions.size > 0) {
      // Merge with existing previous positions, prioritizing current positions
      currentPositions.forEach((pos, nodeId) => {
        this.previousNodePositions.set(nodeId, pos);
      });
      // Emit position updates to parent for preservation
      this.emitUIStateChange({ previousNodePositions: this.previousNodePositions });
    }

    // Get all seed node IDs from graphData
    const seedNodeIds = new Set<string>();
    if (this.graphData?.seed_node_ids) {
      safeArray(this.graphData.seed_node_ids).forEach((id) => {
        seedNodeIds.add(String(id));
      });
    }
    // Fallback to rootNodeId if no seed_node_ids (backward compatibility)
    if (seedNodeIds.size === 0 && this.rootNodeId) {
      seedNodeIds.add(this.rootNodeId);
    }

    // Build filter context and prepare D3 nodes/links
    const filterContext = this.filterService.buildNodeFilterContext(
      this.formState,
      seedNodeIds,
      this.links,
      this.nodes,
      this.apiNodeMap,
      (id: string) => this.findApiNodeById(id)
    );
    const activeNodeIds = this.filterService.buildActiveNodeIdsForFilters(
      this.nodes,
      this.apiNodeMap,
      filterContext,
      this.rootNodeId,
      (id: string) => this.findApiNodeById(id)
    );

    // Prepare D3 nodes with visual states
    // Preserve existing node positions to prevent unnecessary movement
    this.d3Nodes = this.nodes.map((node) => {
      const apiNode = this.apiNodeMap.get(node.id) ?? null;
      const connections = degreeMap.get(node.id) || 0;
      // Base size 80, add 5 per connection
      const symbolSize = 80 + connections * 5;
      
      // If a document is hovered, blur all nodes except the hovered one
      let isBlurred = false;
      let opacity = 1;
      
      if (this.hoveredDocumentId) {
        // Blur all nodes except the hovered one (including root)
        if (node.id !== this.hoveredDocumentId) {
          isBlurred = true;
          opacity = 0.1;
        } else {
          // Highlight the hovered node
          isBlurred = false;
          opacity = 1;
        }
      } else {
        // Normal filter-based blurring when no document is hovered
        const visualState = this.filterService.getNodeVisualState(
          node.id,
          apiNode,
          filterContext,
          this.rootNodeId,
          (id: string) => this.findApiNodeById(id)
        );
        isBlurred = visualState.isBlurred;
        opacity = visualState.opacity;
      }
      
      // Restore previous position if node existed before, otherwise leave undefined for new nodes
      const previousPos = this.previousNodePositions.get(node.id);
      
      return {
        ...node,
        data: apiNode,
        symbolSize,
        opacity,
        isBlurred,
        x: previousPos?.x, // Preserve existing position, undefined for new nodes
        y: previousPos?.y, // Preserve existing position, undefined for new nodes
      };
    });

    // Prepare D3 links with highlighting
    this.d3Links = this.links.map((link) => {
      const sourceId = link.source as string;
      const targetId = link.target as string;
      
      let highlighted = false;
      let linkOpacity = 0.05;
      
      if (this.hoveredDocumentId) {
        // When a document is hovered, blur all links
        highlighted = false;
        linkOpacity = 0.05;
      } else {
        // Normal filter-based highlighting when no document is hovered
        highlighted = this.filterService.shouldHighlightLink(
          sourceId,
          targetId,
          link.__relationshipType,
          filterContext,
          activeNodeIds
        );
        linkOpacity = highlighted ? 1 : 0.05;
      }
      
      return {
        ...link,
        source: link.source,
        target: link.target,
        sourceId,
        targetId,
        highlighted,
        opacity: linkOpacity,
      };
    });

    // Setup callbacks for renderer
    this.graphCallbacks = {
      onNodeClick: (nodeId: string, apiNode: ApiNode) => {
        this.emitDocumentData(apiNode);
        this.highlightDocumentInList(nodeId, apiNode);
      },
      onNodeRightClick: (event: MouseEvent, nodeId: string, apiNode: ApiNode) => {
        this.onRightClickNode(event, apiNode, nodeId);
      },
      onNodeDoubleClick: (nodeId: string, apiNode: ApiNode) => {
        this.expandNodeWithDefaultSchema(nodeId, apiNode);
      },
      formatTooltip: (nodeId: string, apiNode: ApiNode | null, label: string) => {
        if (!apiNode) return label;
        if (apiNode.nhan_ui === "Văn bản") {
          const name = apiNode.thuoc_tinh?.ten_day_du || apiNode.ten_day_du || label || "";
          return formatTooltipText(name).replace(/<br\/>/g, "\n");
        }
        if (apiNode.nhan_ui === "Điều khoản") {
          const clauseTitle =
            apiNode.thuoc_tinh?.tieu_de ||
            apiNode.thuoc_tinh?.vi_tri ||
            label ||
            "";
          return formatTooltipText(clauseTitle).replace(/<br\/>/g, "\n");
        }
        return formatTooltipText(label || "").replace(/<br\/>/g, "\n");
      },
    };
  }



  onRightClickNode(event: MouseEvent, apiNode: ApiNode, nodeId: string): void {
    event.preventDefault();
    const hiddenDescendants = this.getHiddenDescendantsForNode(nodeId);
    const canRestore = hiddenDescendants.size > 0;
    this.contextMenuVisible = true;
    this.contextMenuPosition = { x: event.clientX, y: event.clientY };
    const isRoot = nodeId === this.rootNodeId;
    this.contextMenuItem = { apiNode, nodeId, hiddenDescendants, canRestore, isRoot };
    this.showExpandSubmenu = false; // Reset submenu state when opening menu
    this.cdr.markForCheck(); // Trigger change detection for OnPush strategy
  }

  closeContextMenu(): void {
    this.contextMenuVisible = false;
    this.contextMenuItem = null;
    this.showExpandSubmenu = false;
    this.cdr.markForCheck(); // Trigger change detection for OnPush strategy
  }

  toggleExpandSubmenu(event: Event): void {
    event.stopPropagation();
    this.showExpandSubmenu = !this.showExpandSubmenu; // Toggle instead of always setting to true
    this.cdr.markForCheck(); // Trigger change detection for OnPush strategy
  }

  onShowSubmenu(): void {
    this.showExpandSubmenu = true;
    this.cdr.markForCheck(); // Trigger change detection for OnPush strategy
  }

  onHideSubmenu(): void {
    this.showExpandSubmenu = false;
    this.cdr.markForCheck(); // Trigger change detection for OnPush strategy
  }

  /**
   * Open modal for conditional expansion
   */
  openExpansionModal(): void {
    if (!this.contextMenuItem) return;

    // Save context item before closing menu
    this.savedContextMenuItem = this.contextMenuItem;
    this.closeContextMenu();

    // Clone current form state for modal with default limit values
    this.modalFormState = {
      ...this.formState,
      selectedBoLocMoiQuanHe: [
        ...(this.formState.selectedBoLocMoiQuanHe || []),
      ],
      selectedTinhTrangHieuLuc: [
        ...(this.formState.selectedTinhTrangHieuLuc || []),
      ],
      selectedBoLocLoaiVanBan: [
        ...(this.formState.selectedBoLocLoaiVanBan || []),
      ],
      selectedCoQuanBanHanh: [...(this.formState.selectedCoQuanBanHanh || [])],
      isSearchAdvance: true, // Always show advanced options in modal
      depth: 3, // Fixed default value
      global_limit: 25, // Fixed default value
      limit_per_seed: 25, // Fixed default value
    };

    this.showExpansionModal = true;
  }

  /**
   * Close expansion modal
   */
  closeExpansionModal(): void {
    this.showExpansionModal = false;
    this.modalFormState = null;
    this.savedContextMenuItem = null;
  }

  /**
   * Submit modal form and expand node with custom conditions
   */
  onSubmitModalExpansion(): void {
    if (!this.savedContextMenuItem || !this.modalFormState) return;

    const contextItem = this.savedContextMenuItem;
    const clickedNodeId = contextItem.nodeId;
    const clickedApiNode = contextItem.apiNode;

    if (!clickedNodeId || !clickedApiNode) {
      this.showErrorToast(ERROR_MESSAGES.NO_NODE_INFO);
      return;
    }

    // Get the API node ID (the ID that the backend expects)
    const apiNodeId = getNodeIdFromApiNode(clickedApiNode);
    if (!apiNodeId) {
      this.showErrorToast(ERROR_MESSAGES.NO_NODE_ID);
      return;
    }

    // Build request body from modal form state using form service
    // Override limit values to fixed defaults
    const modalStateWithDefaults = {
      ...this.modalFormState,
      global_limit: 25,
      limit_per_seed: 25,
    };
    const body = this.formService.buildRequestBody(modalStateWithDefaults, String(apiNodeId));

    // If this node was previously expanded, remove its previous expansion results first
    if (this.expansionService.hasExpansionHistory(apiNodeId)) {
      this.removePreviousExpansion(apiNodeId);
    }

    // Close modal immediately before making API call
    this.closeExpansionModal();

    // Make the API call with loading state on graph body
    this.isLoadingGraph = true;
    this.cdr.markForCheck(); // Update loading state immediately
    this.viewDetailFile.getDoThi(body).subscribe(
      (res) => {
        if (this.graphData && res) {
          this.mergeGraphData(res, apiNodeId);
        } else {
          this.graphData = res;
        }

        this.viewDetailFile.graphData.next(this.graphData);
        this.transformGraphData();
        this.showSuccessToast(SUCCESS_MESSAGES.EXPANSION_SUCCESS);
        this.isGraphError = false;
        this.isLoadingGraph = false;
        this.cdr.markForCheck(); // Trigger change detection after data update
      },
      (error) => {
        if (error?.error === "Không tìm thấy dữ liệu đồ thị") {
          this.showInfoToast("Không có dữ liệu đồ thị của văn bản");
        } else {
          this.showErrorToast(ERROR_MESSAGES.EXPANSION_FAILED);
        }
        this.isLoadingGraph = false;
        this.cdr.markForCheck(); // Trigger change detection after error
      }
    );
  }

  /**
   * Build date filter payload from a specific form state
   */

  toggleDocumentList(): void {
    const wasExpanded = this.documentListExpanded;
    this.documentListExpanded = !this.documentListExpanded;
    // Emit panel state update to parent for preservation
    this.emitUIStateChange({ documentListExpanded: this.documentListExpanded });
    this.cdr.markForCheck(); // Trigger change detection for OnPush strategy
    
    // If panel is being opened and we have a highlighted document, ensure it's visible
    if (!wasExpanded && this.documentListExpanded && this.highlightedDocumentId) {
      // Scroll to highlighted document after panel opens
      setTimeout(() => {
        if (typeof document === "undefined") {
          return;
        }
        const escapeFn =
          typeof window !== "undefined" && (window as any)?.CSS?.escape
            ? (window as any).CSS.escape
            : (value: string) => value.replace(/"/g, '\\"');
        const safeNodeId = escapeFn(this.highlightedDocumentId!);
        const selector = `[data-document-row="${safeNodeId}"]`;
        const element = document.querySelector(selector) as HTMLElement | null;
        if (element) {
          element.scrollIntoView({ behavior: "smooth", block: "nearest" });
        }
      }, 100); // Slightly longer delay to ensure panel is fully rendered
    }
  }

  @HostListener("document:click")
  onDocumentClick(): void {
    this.closeContextMenu();
  }

  /**
   * Expand node with default schema (fixed parameters)
   * Called from context menu
   */
  onExpandNodeWithDefaultSchema(): void {
    if (!this.contextMenuItem) return;

    const contextItem = this.contextMenuItem;
    this.closeContextMenu();

    const clickedNodeId = contextItem.nodeId;
    const clickedApiNode = contextItem.apiNode;

    if (!clickedNodeId || !clickedApiNode) {
      this.showErrorToast(ERROR_MESSAGES.NO_NODE_INFO);
      return;
    }

    this.expandNodeWithDefaultSchema(clickedNodeId, clickedApiNode);
  }

  /**
   * Core logic for expanding a node with default schema
   * Can be called from context menu or double-click
   */
  private expandNodeWithDefaultSchema(nodeId: string, apiNode: ApiNode): void {
    if (!nodeId || !apiNode) {
      this.showErrorToast(ERROR_MESSAGES.NO_NODE_INFO);
      return;
    }

    // Get the API node ID (the ID that the backend expects)
    const apiNodeId = getNodeIdFromApiNode(apiNode);
    if (!apiNodeId) {
      this.showErrorToast(ERROR_MESSAGES.NO_NODE_ID);
      return;
    }

    // Build request body with fixed default schema values
    const body: GraphRequestBody = {
      co_quan_ban_hanh: [],
      depth: 1,
      global_limit: 25,
      limit_per_seed: 25,
      loai_van_ban: [],
      node_ids: [String(apiNodeId)],
      relationship_types: [],
      target_node_type: "VAN_BAN",
      tinh_trang_hieu_luc: [],
      ban_hanh_year_from: null,
      ban_hanh_year_to: null,
      hieu_luc_year_from: null,
      hieu_luc_year_to: null,
    };

    // If this node was previously expanded, remove its previous expansion results first
    if (this.expansionService.hasExpansionHistory(apiNodeId)) {
      this.removePreviousExpansion(apiNodeId);
    }

    // Make the API call
    this.isLoadingGraph = true;
    this.cdr.markForCheck(); // Update loading state immediately
    this.viewDetailFile.getDoThi(body).subscribe(
      (res) => {
        if (this.graphData && res) {
          this.mergeGraphData(res, apiNodeId);
        } else {
          this.graphData = res;
        }

        this.viewDetailFile.graphData.next(this.graphData);
        this.transformGraphData();
        this.showSuccessToast(SUCCESS_MESSAGES.EXPANSION_SUCCESS);
        this.isLoadingGraph = false;
        this.cdr.markForCheck(); // Trigger change detection after data update
      },
      (error) => {
        if (error?.error === "Không tìm thấy dữ liệu đồ thị") {
          this.showInfoToast("Không có dữ liệu đồ thị của văn bản");
        } else {
          this.showErrorToast(ERROR_MESSAGES.EXPANSION_FAILED);
        }
        this.isLoadingGraph = false;
        this.cdr.markForCheck(); // Trigger change detection after error
      }
    );
  }

  private mergeGraphData(newData: GraphApiResponse, seedNodeId: string): void {
    if (!this.graphData || !newData) return;

    // Use expansion service to merge
    const result = this.expansionService.mergeGraphData(
      this.graphData,
      newData,
      seedNodeId
    );

    this.graphData = result.mergedData;
    this.cacheService.invalidateCaches();
  }

  /**
   * Remove nodes and relationships from a previous expansion of a node
   */
  private removePreviousExpansion(seedNodeId: string): void {
    if (!this.graphData) return;

    // Use expansion service
    const result = this.expansionService.removePreviousExpansion(
      this.graphData,
      seedNodeId,
      this.rootNodeId,
      this.hiddenNodeIds
    );

    this.graphData = result.updatedData;
    // Remove node IDs from hidden set
    result.removedNodeIds.forEach((id) => this.hiddenNodeIds.delete(id));
    // Emit hidden nodes update to parent for preservation
    if (result.removedNodeIds.size > 0) {
      this.emitUIStateChange({ hiddenNodeIds: this.hiddenNodeIds });
    }
  }


  private getDescendantNodeIds(nodeId: string): Set<string> {
    return this.expansionService.getDescendantNodeIds(
      nodeId,
      this.graphData,
      this.rootNodeId,
      this.hiddenNodeIds
    );
  }

  private getHiddenDescendantsForNode(nodeId: string): Set<string> {
    return this.expansionService.getHiddenDescendantsForNode(
      nodeId,
      this.graphData,
      this.rootNodeId,
      this.hiddenNodeIds
    );
  }

  onCollapseNode(): void {
    if (!this.contextMenuItem) return;

    const contextItem = this.contextMenuItem;
    this.closeContextMenu();

    const nodeId = contextItem.nodeId;

    if (!nodeId) {
      this.showErrorToast(ERROR_MESSAGES.NO_NODE_INFO);
      return;
    }

    if (nodeId === this.rootNodeId) {
      this.showWarningToast(ERROR_MESSAGES.CANNOT_HIDE_ROOT);
      return;
    }

    const descendants = this.getDescendantNodeIds(nodeId);
    const nodesToHide = new Set<string>(descendants);
    nodesToHide.add(nodeId);

    let hiddenCount = 0;
    nodesToHide.forEach((id) => {
      if (id === this.rootNodeId) {
        return;
      }
      if (!this.hiddenNodeIds.has(id)) {
        this.hiddenNodeIds.add(id);
        hiddenCount++;
      }
    });

    if (hiddenCount === 0) {
      this.showInfoToast(INFO_MESSAGES.NO_NEW_NODES_HIDDEN);
      return;
    }

    // Emit hidden nodes update to parent for preservation
    this.emitUIStateChange({ hiddenNodeIds: this.hiddenNodeIds });

    this.showDocumentTable = false;
    this.emitUIStateChange({ showDocumentTable: false });
    this.transformGraphData();
    // Recalculate filter options based on currently visible nodes
    this.recalculateFilterOptionsFromVisibleGraph();

    this.showSuccessToast(SUCCESS_MESSAGES.NODES_HIDDEN);
    this.cdr.markForCheck(); // Trigger change detection for OnPush strategy
  }

  onRestoreNode(): void {
    if (!this.contextMenuItem) return;

    const contextItem = this.contextMenuItem;
    this.closeContextMenu();

    // Only root node can trigger full graph restore
    if (!contextItem.isRoot) {
      return;
    }

    if (!this.formState) {
      return;
    }

    // Clear all active filters in form state
    this.formState.selectedBoLocMoiQuanHe = [];
    this.formState.selectedTinhTrangHieuLuc = [];
    this.formState.selectedBoLocLoaiVanBan = [];
    this.formState.selectedCoQuanBanHanh = [];
    this.formState.dateFilterMode = null;
    this.formState.dateFilterFrom = null;
    this.formState.dateFilterTo = null;

    // Reset hidden nodes and expansion state
    this.hiddenNodeIds.clear();
    // Emit hidden nodes update to parent for preservation
    this.emitUIStateChange({ hiddenNodeIds: this.hiddenNodeIds });

    const targetId = this.originalDocumentEsId || this.currentDocumentEsId;
    if (!targetId) {
      this.showErrorToast(ERROR_MESSAGES.NO_DOCUMENT_ID);
      return;
    }

    // Re-fetch the graph using default request body (no filters),
    // preserving current view mode and depth/limit settings.
    const body = this.formService.buildDefaultRequestBody(
      this.formState.search_legal_term,
      targetId,
      this.formState.depth,
      this.formState.global_limit,
      this.formState.limit_per_seed
    );

    this.isLoadingGraph = true;
    this.cdr.markForCheck(); // Update loading state immediately
    this.viewDetailFile.getDoThi(body).subscribe(
      (res) => {
        this.graphData = res;
        this.resetGraphState();
        this.viewDetailFile.graphData.next(this.graphData);
        this.updateFilterOptionsFromGraph();
        this.transformGraphData();
        this.isGraphError = false;
        this.isLoadingGraph = false;
        this.cdr.markForCheck(); // Trigger change detection after data update
      },
      (error) => {
        this.handleApiError(error);
        this.isLoadingGraph = false;
        this.cdr.markForCheck(); // Trigger change detection after error
      }
    );
  }

  private findApiNodeById(nodeId: string): ApiNode | null {
    if (!nodeId) return null;
    if (this.apiNodeMap.has(nodeId)) return this.apiNodeMap.get(nodeId)!;

    // Fallback: search in current graphData.nodes if present
    const nodes = safeArray(this.graphData?.nodes);
    for (const n of nodes) {
      const idCandidate = getNodeIdFromApiNode(n);
      if (idCandidate === nodeId) return n;
    }
    return null;
  }

  private transformGraphData(): void {
    if (!this.graphData) return;

    // Invalidate caches when transforming
    this.cacheService.invalidateCaches();
    this.invalidateDocumentCaches();

    // Use transformer service
    const result = this.transformerService.transformGraphData(
      this.graphData,
      this.hiddenNodeIds
    );

    this.nodes = result.nodes;
    this.links = result.links;
    this.apiNodeMap = result.apiNodeMap;
    this.rootNodeId = result.rootNodeId;

    // Update root document data if available
    if (result.rootDocumentData) {
      this.dataFile = result.rootDocumentData;
      this.currentNodeId = this.rootNodeId;
    }

    this.renderGraph();

    // Re-apply filters if any are selected
    if (
      this.formState?.selectedBoLocMoiQuanHe &&
      this.formState.selectedBoLocMoiQuanHe.length > 0
    ) {
      setTimeout(() => {
        this.filterByRelationshipTypes(this.formState.selectedBoLocMoiQuanHe);
      }, 0);
    }

    if (
      this.formState?.selectedCoQuanBanHanh &&
      this.formState.selectedCoQuanBanHanh.length > 0
    ) {
      setTimeout(() => {
        this.filterByCoQuanBanHanh(this.formState.selectedCoQuanBanHanh);
      }, 0);
    }

    if (
      this.formState?.selectedBoLocLoaiVanBan &&
      this.formState.selectedBoLocLoaiVanBan.length > 0
    ) {
      setTimeout(() => {
        this.filterByLoaiVanBan(this.formState.selectedBoLocLoaiVanBan);
      }, 0);
    }

    if (
      this.formState?.selectedTinhTrangHieuLuc &&
      this.formState.selectedTinhTrangHieuLuc.length > 0
    ) {
      setTimeout(() => {
        this.filterByTrangThaiHieuLuc(this.formState.selectedTinhTrangHieuLuc);
      }, 0);
    }

    if (
      this.formState?.dateFilterMode &&
      this.formState.dateFilterFrom !== null &&
      this.formState.dateFilterTo !== null
    ) {
      setTimeout(() => {
        this.filterByDateRange();
      }, 0);
    }

    // Update document list whenever graph data is transformed
    this.updateDocumentList();
    // Trigger change detection for OnPush strategy
    this.cdr.markForCheck();
  }

  private emitDocumentData(apiNode: ApiNode): void {
    // Use document list service to convert
    const documentData = this.documentListService.convertApiNodeToDocumentData(apiNode);

    // Update local dataFile and emit to parent
    this.dataFile = documentData;
    this.currentNodeId = getNodeIdFromApiNode(apiNode);
    this.documentClicked.emit(documentData);
    // Show document details table when clicking a node (not in TimKiem mode)
    if (!this.isTimKiemMode) {
      this.showDocumentTable = true;
      this.emitUIStateChange({ showDocumentTable: true });
    } else {
      // In TimKiem mode, show the right panel when a document is selected
      this.showRightPanel = true;
    }

    // Check if this is a clause node and fetch content
    this.isClauseNode = apiNode.nhan_ui === "Điều khoản";
    if (this.isClauseNode) {
      this.fetchClauseContent(apiNode);
    } else {
      // Clear clause content for non-clause nodes
      this.clauseContent = "";
    }
    this.cdr.markForCheck(); // Trigger change detection for OnPush strategy
  }

  /**
   * Highlight a document inside the panel list when its graph node is selected
   * Only highlights if the panel is already open, but always stores the selected document ID
   * so it can be highlighted when the panel is opened manually later
   */
  private highlightDocumentInList(nodeId: string, apiNode: ApiNode): void {
    if (!nodeId || apiNode?.nhan_ui !== "Văn bản") {
      this.highlightedDocumentId = null;
      return;
    }

    const documentExists = this.documentList.some((doc) => doc.id === nodeId);
    if (!documentExists) {
      this.highlightedDocumentId = null;
      return;
    }

    // Always store the selected document ID so it can be highlighted when panel opens manually
    this.highlightedDocumentId = nodeId;
    
    // Only highlight and scroll if the panel is already open
    // Don't auto-open the panel or change tabs
    if (this.documentListExpanded) {
      // Scroll the highlighted row into view to make it obvious for the user
      setTimeout(() => {
        if (typeof document === "undefined") {
          return;
        }
        const escapeFn =
          typeof window !== "undefined" && (window as any)?.CSS?.escape
            ? (window as any).CSS.escape
            : (value: string) => value.replace(/"/g, '\\"');
        const safeNodeId = escapeFn(nodeId);
        const selector = `[data-document-row="${safeNodeId}"]`;
        const element = document.querySelector(selector) as HTMLElement | null;
        if (element) {
          element.scrollIntoView({ behavior: "smooth", block: "nearest" });
        }
      }, 0);
    }
  }

  /**
   * Fetch clause content from API for "Điều khoản" nodes
   */
  private fetchClauseContent(apiNode: ApiNode): void {
    // Extract clause_id and doc_id
    const nodeId = getNodeIdFromApiNode(apiNode);
    if (!nodeId) {
      this.clauseContent = "";
      return;
    }

    // Extract clause_id (part before #) and doc_id from node's thuoc_tinh.doc_ID
    const clauseId = nodeId.includes("#") ? nodeId.split("#")[0] : nodeId;
    const docId = apiNode.thuoc_tinh?.doc_ID;

    if (!clauseId || !docId) {
      this.clauseContent = "";
      return;
    }

    // Call API
    this.isLoadingClauseContent = true;
    this.hasClauseContentError = false;
    this.clauseContent = "";
    this.cdr.markForCheck(); // Update loading state immediately

    this.viewDetailFile.getLawClauseContent(clauseId, String(docId)).subscribe(
      (response: any) => {
        const rawContent = response?.raw_content || "";
        // Convert newlines to <br> tags for proper HTML display
        this.clauseContent = rawContent.replace(/\n/g, "<br>");
        this.isLoadingClauseContent = false;
        this.hasClauseContentError = false;
        this.cdr.markForCheck(); // Trigger change detection after content loaded
      },
      (error) => {
        console.error("Error fetching clause content:", error);
        this.clauseContent = "";
        this.isLoadingClauseContent = false;
        this.hasClauseContentError = true;
        this.showErrorToast("Không thể tải nội dung điều khoản");
        this.cdr.markForCheck(); // Trigger change detection after error
      }
    );
  }

  /**
   * Get formatted clause content for display
   */
  getFormattedClauseContent(): string {
    if (this.isLoadingClauseContent) {
      return "Đang tải...";
    }
    return this.clauseContent || "Không có nội dung";
  }

  onCloseDocumentTable(): void {
    this.showDocumentTable = false;
    this.emitUIStateChange({ showDocumentTable: false });
    this.clauseContent = "";
    this.isClauseNode = false;
    this.isLoadingClauseContent = false;
    this.hasClauseContentError = false;
    this.cdr.markForCheck(); // Trigger change detection for OnPush strategy
  }

  // Toggle between graph view and full table view
  toggleFullDocumentTable(): void {
    this.isFullTableView = !this.isFullTableView;

    if (this.isFullTableView) {
      // Ensure table selection reflects current document selections
      this.invalidateDocumentCaches();
      const paginated = this.getPaginatedDocuments();
      this.selectedTableRows = paginated.filter((item) => item.selected);
    } else {
      this.selectedTableRows = [];
    }

    this.cdr.detectChanges();
  }

  /**
   * Returns documentList filtered by fullTableSearch (matches title, loại văn bản, số hiệu)
   * Also adds properties for ngx-datatable sorting
   * Note: Returns same object references to maintain selection state
   * Uses memoization to avoid recomputing when inputs haven't changed
   */
  getFilteredDocuments(): DocumentTableRow[] {
    const cacheKey = `${this.fullTableSearch}_${this.documentList.length}`;
    
    // Return cached result if inputs haven't changed
    if (this.filteredDocumentsCache && this.filteredDocumentsCacheKey === cacheKey) {
      return this.filteredDocumentsCache;
    }

    const query = (this.fullTableSearch || "").toLowerCase().trim();
    let filtered = query 
      ? this.documentList.filter((item) => {
          const attrs = item.apiNode?.thuoc_tinh || {};
          const title = (attrs.ten_day_du || item.title || "").toLowerCase();
          const loai = (attrs.loai_van_ban || "").toLowerCase();
          const soHieu = (attrs.so_hieu || "").toLowerCase();
          return (
            title.includes(query) || loai.includes(query) || soHieu.includes(query)
          );
        })
      : this.documentList;
    
    // Add properties for ngx-datatable sorting while maintaining object references
    const result: DocumentTableRow[] = filtered.map((item) => {
      // If properties already exist, reuse the object, otherwise add them
      if ('loai_van_ban' in item && (item as DocumentTableRow).loai_van_ban !== undefined) {
        return item as DocumentTableRow;
      }
      return Object.assign(item, {
        loai_van_ban: item.apiNode?.thuoc_tinh?.loai_van_ban || "",
        so_hieu: item.apiNode?.thuoc_tinh?.so_hieu || "",
        co_quan_ban_hanh: item.apiNode?.thuoc_tinh?.co_quan_ban_hanh || "",
        ngay_ban_hanh: item.apiNode?.thuoc_tinh?.ngay_ban_hanh || "",
        ngay_co_hieu_luc: item.apiNode?.thuoc_tinh?.ngay_co_hieu_luc || "",
        tinh_trang_hieu_luc: item.apiNode?.thuoc_tinh?.tinh_trang_hieu_luc || "",
      }) as DocumentTableRow;
    });
    
    // Cache the result
    this.filteredDocumentsCache = result;
    this.filteredDocumentsCacheKey = cacheKey;
    
    return result;
  }

  /**
   * Truncates document name for display in table
   * Uses a high character limit to maximize words, CSS will handle final truncation based on cell width
   */
  getTruncatedDocumentName(item: DocumentListItem | DocumentTableRow, maxChars: number = 50): string {
    const documentName = item.apiNode?.thuoc_tinh?.ten_day_du || item.title || "";
    return truncate(documentName, maxChars);
  }

  /**
   * Returns documentList filtered by documentListSearch (for panel view)
   * Searches only in the displayed fields: document type (loai_van_ban) and document number (so_hieu)
   * This matches what's actually shown in the list (item.title = "loai_van_ban - so_hieu")
   * Does NOT search in ten_day_du to avoid false matches (e.g., "Nghị định" matching "Luật" 
   * because ten_day_du contains "Luật Hôn nhân gia đình")
   */
  getFilteredDocumentList() {
    const query = (this.documentListSearch || "").toLowerCase().trim();
    if (!query) return this.documentList;
    return this.documentList.filter((item) => {
      const attrs = item.apiNode?.thuoc_tinh || {};
      // Search only in what's displayed: loai_van_ban and so_hieu
      // item.title is constructed as "loai_van_ban - so_hieu"
      const displayedTitle = (item.title || "").toLowerCase();
      const loai = (attrs.loai_van_ban || "").toLowerCase();
      const soHieu = (attrs.so_hieu || "").toLowerCase();
      // Match if query appears in displayed title, document type, or document number
      return (
        displayedTitle.includes(query) || loai.includes(query) || soHieu.includes(query)
      );
    });
  }

  get totalItem(): number {
    return this.getFilteredDocuments().length;
  }

  /**
   * Get paginated documents for the current page
   * Used with externalPaging in ngx-datatable
   * Uses memoization to avoid recomputing when inputs haven't changed
   */
  getPaginatedDocuments(): DocumentTableRow[] {
    const cacheKey = `${this.page}_${this.pageSize}_${this.fullTableSearch}_${this.documentList.length}`;
    
    // Return cached result if inputs haven't changed
    if (this.paginatedDocumentsCache && this.paginatedDocumentsCacheKey === cacheKey) {
      return this.paginatedDocumentsCache;
    }

    const filtered = this.getFilteredDocuments();
    const startIndex = (this.page - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    const result = filtered.slice(startIndex, endIndex);
    
    // Cache the result
    this.paginatedDocumentsCache = result;
    this.paginatedDocumentsCacheKey = cacheKey;
    
    return result;
  }

  /**
   * Invalidate document-related caches
   */
  private invalidateDocumentCaches(): void {
    this.filteredDocumentsCache = null;
    this.filteredDocumentsCacheKey = "";
    this.paginatedDocumentsCache = null;
    this.paginatedDocumentsCacheKey = "";
  }

  /**
   * Handle table row selection
   */
  onTableSelect(event: { selected: DocumentTableRow[] }): void {
    // event.selected contains the actual row objects from the current page
    // Update selectedTableRows to match event.selected (same object references)
    this.selectedTableRows = event.selected ? [...event.selected] : [];
    
    // Get IDs of selected rows from current page
    const selectedIds = new Set(
      event.selected ? event.selected.map((row) => row.id) : []
    );
    
    // Only update rows that are on the current page
    const paginatedRows = this.getPaginatedDocuments();
    paginatedRows.forEach((row) => {
      const shouldBeSelected = selectedIds.has(row.id);
      if (row.selected !== shouldBeSelected) {
        row.selected = shouldBeSelected;
        this.updateSelectedFiles(row, shouldBeSelected);
      }
    });
    
    // Force change detection to update UI
    this.cdr.detectChanges();
    this.updateSelectAllCheckboxState();
  }

  /**
   * Handle table row activation (click)
   */
  onTableActivate(event: { type: string; row?: DocumentTableRow }): void {
    if (event.type === "click" && event.row) {
      const apiNode = event.row.apiNode;
      if (apiNode) {
        this.emitDocumentData(apiNode);
        this.highlightDocumentInList(event.row.id, apiNode);
      }
    }
  }

  /**
   * Get row class for highlighting
   */
  getRowClass = (row: DocumentTableRow): string => {
    return row.id === this.highlightedDocumentId ? 'document-row--highlighted' : '';
  }

  /**
   * Date comparator for ngx-datatable sorting
   */
  dateComparator = (propA: string, propB: string): number => {
    const dateA = propA ? new Date(propA).getTime() : 0;
    const dateB = propB ? new Date(propB).getTime() : 0;
    return dateA - dateB;
  }

  onPageChange(page: number): void {
    this.page = page;
    // Invalidate cache when page changes
    this.invalidateDocumentCaches();
    // Sync selectedTableRows with paginated documents that are selected
    const paginated = this.getPaginatedDocuments();
    this.selectedTableRows = paginated.filter((item) => item.selected);
  }
  onSelectAllDocumentsChange(event: { target: { checked: boolean } }): void {
    const isChecked = event.target.checked;
    // Use the appropriate filtered list based on current view
    const filteredList = this.isFullTableView 
      ? this.getFilteredDocuments() 
      : this.getFilteredDocumentList();
    filteredList.forEach((item) => {
      item.selected = isChecked;
      this.updateSelectedFiles(item, isChecked);
    });
    // Update checkbox state based on filtered results
    this.updateSelectAllCheckboxState();
  }

  onDocumentSelectChange(event: { target: { checked: boolean } }, item: DocumentListItem): void {
    const isChecked = event.target.checked;
    item.selected = isChecked;
    this.updateSelectedFiles(item, isChecked);
    // Update "select all" checkbox based on filtered documents
    this.updateSelectAllCheckboxState();
  }

  /**
   * Determine if all documents are selected (for header checkbox)
   * When search filter is active, evaluate against filtered documents only
   * When no search filter, evaluate against entire documentList
   */
  isAllDocumentsSelected(): boolean {
    if (!this.isFullTableView) {
      return false;
    }

    const hasSearchFilter = (this.fullTableSearch || "").trim().length > 0;
    if (hasSearchFilter) {
      const filteredList = this.getFilteredDocuments();
      if (filteredList.length === 0) {
        return false;
      }
      return filteredList.every((doc) => doc.selected);
    }

    if (this.documentList.length === 0) {
      return false;
    }
    return this.documentList.every((doc) => doc.selected);
  }

  /**
   * Handle header checkbox change for full table view
   * Without search filter: select/deselect all documents across all pages
   * With search filter: select/deselect only the filtered subset
   */
  onHeaderCheckboxChange(event: { target: { checked: boolean } }): void {
    if (!this.isFullTableView) {
      return;
    }

    const isChecked = event.target.checked;
    const hasSearchFilter = (this.fullTableSearch || "").trim().length > 0;
    const targetList = hasSearchFilter ? this.getFilteredDocuments() : this.documentList;

    targetList.forEach((item) => {
      if (item.selected !== isChecked) {
        item.selected = isChecked;
        this.updateSelectedFiles(item, isChecked);
      }
    });

    // Sync the current page selection state
    const paginated = this.getPaginatedDocuments();
    this.selectedTableRows = paginated.filter((item) => item.selected);

    this.cdr.detectChanges();
    this.updateSelectAllCheckboxState();
  }

  /**
   * Update the "select all" checkbox state based on filtered documents
   * Checkbox is checked only if all filtered documents are selected
   */
  private updateSelectAllCheckboxState(): void {
    // Use the appropriate filtered list based on current view
    const filteredList = this.isFullTableView 
      ? this.getFilteredDocuments() 
      : this.getFilteredDocumentList();
    if (filteredList.length === 0) {
      this.selectAllDocuments = false;
      return;
    }
    this.selectAllDocuments = filteredList.every((doc) => doc.selected);
  }

  /**
   * Called when the document list search input changes
   * Updates the "select all" checkbox state based on the new filtered results
   */
  onDocumentListSearchChange(): void {
    this.updateSelectAllCheckboxState();
  }

  /**
   * Called when the full table search input changes
   * Updates the "select all" checkbox state based on the new filtered results
   * When search is cleared, if all documents are selected, keep checked state, otherwise uncheck
   */
  onFullTableSearchChange(): void {
    // Reset to first page when search changes
    this.page = 1;
    
    // Invalidate cache when search changes
    this.invalidateDocumentCaches();
    
    // Sync selectedTableRows with paginated documents that are selected
    const paginated = this.getPaginatedDocuments();
    this.selectedTableRows = paginated.filter((item) => item.selected);
    
    // Update checkbox state based on filtered results
    // This will automatically check if all filtered documents are selected
    this.updateSelectAllCheckboxState();
  }

  /**
   * Update selectedFiles array when a document is selected/deselected
   */
  private updateSelectedFiles(item: DocumentListItem, isSelected: boolean): void {
    const rawId = item.apiNode?.thuoc_tinh?.ID;
    const numericId = Number(rawId);

    if (Number.isNaN(numericId)) {
      return;
    }

    if (isSelected) {
      // Add to selectedFiles if not already present
      if (!this.selectedFiles.some((f) => f.id === numericId)) {
        const documentData = this.documentListService.convertApiNodeToDocumentData(item.apiNode);
        this.selectedFiles.push({
          id: numericId,
          ...documentData,
        } as DocumentData & { id: number });
      }
    } else {
      // Remove from selectedFiles
      this.selectedFiles = this.selectedFiles.filter((f) => f.id !== numericId);
    }
  }

  /**
   * Convert ApiNode to document data format for saving
   */
  /**
   * Save selected documents to workspace
   */
  saveHistoryFiles(): void {
    const workspaceId = this.route.snapshot.params.id || localStorage.getItem("workspace_id");
    if (!workspaceId) {
      this.showErrorToast("Không tìm thấy workspace ID");
      return;
    }

    this.isSavingFiles = true;
    this.documentListService.saveDocuments(this.selectedFiles, workspaceId).subscribe(
      (response) => {
        this.documentListService.handleSaveResponse(response);
        this.workspace.isSaveFileFromSearch.next(true);
        // Clear selections after successful save
        this.selectedFiles = [];
        this.documentList.forEach((item) => {
          item.selected = false;
        });
        this.selectAllDocuments = false;
        this.isSavingFiles = false;
      },
      (error) => {
        const errorMessage = this.documentListService.handleSaveError(error);
        this.showErrorToast(errorMessage);
        this.isSavingFiles = false;
      }
    );
  }

  /* -------------------------------
   * Helper Methods
   * ----------------------------- */

  /**
   * Emit UI state changes to parent component for preservation
   */
  private emitUIStateChange(updates: {
    hiddenNodeIds?: Set<string>;
    previousNodePositions?: Map<string, { x: number; y: number }>;
    documentListExpanded?: boolean;
    showDocumentTable?: boolean;
  }): void {
    this.uiStateChange.emit(updates);
  }

  /**
   * Clear all selected documents and uncheck in both views
   */
  clearSelectedDocuments(): void {
    this.selectedFiles = [];
    this.selectAllDocuments = false;
    // Uncheck items in documentList panel/table
    this.documentList.forEach((item) => (item.selected = false));
    this.selectedTableRows = [];
    this.updateSelectAllCheckboxState();
    this.cdr.detectChanges();
  }

  /**
   * Clears the current graph visualization and related UI state
   * Used when API returns no data or encounters an error
   */
  private clearGraphVisualizationState(): void {
    this.nodes = [];
    this.links = [];
    this.d3Nodes = [];
    this.d3Links = [];
    this.apiNodeMap = new Map();
    this.rootNodeId = "";
    this.showDocumentTable = false;
    this.emitUIStateChange({ showDocumentTable: false });
    this.highlightedDocumentId = null;
    this.isFullTableView = false;
    this.selectedTableRows = [];
    this.selectAllDocuments = false;
    this.documentList = [];
    this.selectedFiles = [];
    this.clauseContent = "";
    this.isClauseNode = false;
    this.isLoadingClauseContent = false;
    this.hasClauseContentError = false;
    this.hiddenNodeIds.clear();
    this.emitUIStateChange({ hiddenNodeIds: this.hiddenNodeIds });
    this.previousNodePositions.clear(); // Clear position cache when graph is cleared
    this.emitUIStateChange({ previousNodePositions: this.previousNodePositions });
    this.cacheService.invalidateCaches();
    this.expansionService.clearExpansionHistory();
  }



  /**
   * Reset graph state when loading new graph
   */
  private resetGraphState(): void {
    this.expansionService.clearExpansionHistory();
    this.hiddenNodeIds.clear();
    this.emitUIStateChange({ hiddenNodeIds: this.hiddenNodeIds });
    this.cacheService.invalidateCaches();
    this.showDocumentTable = false;
    this.emitUIStateChange({ showDocumentTable: false });
  }

  /**
   * Handle API errors consistently
   */
  private handleApiError(error: any): void {
    this.graphData = null;
    this.viewDetailFile.graphData.next(null);
    this.clearGraphVisualizationState();
    this.isGraphError = true;

    if (error?.error === "Không tìm thấy dữ liệu đồ thị") {
      this.showInfoToast("Không có dữ liệu đồ thị của văn bản");
      return;
    }

    const errorMessage =
      error?.detail ||
      error?.error?.message ||
      error?.message ||
      ERROR_MESSAGES.GRAPH_UPDATE_FAILED;

    this.showErrorToast(errorMessage);
  }

  /**
   * Show success toast notification
   */
  private showSuccessToast(message: string): void {
    this.toast.success(message, "Thành công", TOAST_CONFIG.SUCCESS);
  }

  /**
   * Show error toast notification
   */
  private showErrorToast(message: string): void {
    this.toast.error(message, "Lỗi", TOAST_CONFIG.ERROR);
  }

  /**
   * Show warning toast notification
   */
  private showWarningToast(message: string): void {
    this.toast.warning(message, "Thông báo", TOAST_CONFIG.ERROR);
  }

  /**
   * Show info toast notification
   */
  private showInfoToast(message: string): void {
    this.toast.info(message, "Thông báo", TOAST_CONFIG.ERROR);
  }

  /**
   * Filter and highlight nodes/edges based on selected relationship types
   */
  private filterByRelationshipTypes(selectedTypes: string[]): void {
    this.renderGraphWithD3();
    this.cdr.markForCheck(); // Trigger change detection for OnPush strategy
  }

  /**
   * Handle selection change for relationship types
   */
  onMoiQuanHeSelectionChange(selectedValues: string[]): void {
    if (this.formState) {
      this.formState.selectedBoLocMoiQuanHe = selectedValues;
      // Apply visual filter instead of API call
      this.filterByRelationshipTypes(selectedValues);
    }
  }

  /**
   * Generic filter function that filters nodes based on a matcher function
   * Reusable for both "Cơ quan ban hành" and "Loại văn bản" filters
   */
  private filterNodesByProperty(
    _selectedValues: string[],
    _getNodeValue: (apiNode: ApiNode) => string
  ): void {
    this.renderGraphWithD3();
    this.cdr.markForCheck(); // Trigger change detection for OnPush strategy
  }

  /**
   * Filter and highlight nodes based on selected issuing authorities
   */
  private filterByCoQuanBanHanh(selectedAuthorities: string[]): void {
    this.filterNodesByProperty(
      selectedAuthorities,
      (apiNode: ApiNode) => apiNode.thuoc_tinh?.co_quan_ban_hanh || ""
    );
  }

  /**
   * Filter and highlight nodes based on selected document types
   */
  private filterByLoaiVanBan(selectedTypes: string[]): void {
    this.filterNodesByProperty(
      selectedTypes,
      (apiNode: ApiNode) => apiNode.thuoc_tinh?.loai_van_ban || ""
    );
  }

  /**
   * Filter and highlight nodes based on selected validity status
   */
  private filterByTrangThaiHieuLuc(selectedStatuses: string[]): void {
    this.filterNodesByProperty(
      selectedStatuses,
      (apiNode: ApiNode) => apiNode.thuoc_tinh?.tinh_trang_hieu_luc || ""
    );
  }

  /**
   * Filter and highlight nodes based on date range
   */
  private filterByDateRange(): void {
    this.renderGraphWithD3();
    this.cdr.markForCheck(); // Trigger change detection for OnPush strategy
  }


  /**
   * Handle selection change for issuing authorities
   */
  onCoQuanBanHanhSelectionChange(selectedValues: string[]): void {
    if (this.formState) {
      this.formState.selectedCoQuanBanHanh = selectedValues;
      // Apply visual filter
      this.filterByCoQuanBanHanh(selectedValues);
    }
  }

  /**
   * Handle selection change for document types
   */
  onLoaiVanBanSelectionChange(selectedValues: string[]): void {
    if (this.formState) {
      this.formState.selectedBoLocLoaiVanBan = selectedValues;
      // Apply visual filter
      this.filterByLoaiVanBan(selectedValues);
    }
  }

  /**
   * Handle selection change for validity status
   */
  onTrangThaiHieuLucSelectionChange(selectedValues: string[]): void {
    if (this.formState) {
      this.formState.selectedTinhTrangHieuLuc = selectedValues;
      // Apply visual filter
      this.filterByTrangThaiHieuLuc(selectedValues);
    }
  }

  /**
   * Handle depth slider change
   */
  onDepthSliderChange(value: number): void {
    if (this.formState) {
      this.formState.depth = Math.round(value);
      this.validateDepth({ target: { value: this.formState.depth } });
      // Auto-trigger API call
      this.onSubmitForm();
    }
  }

  /**
   * Handle view mode change (search_legal_term)
   * Sends request with default body (no filters), then applies frontend filters
   */
  onViewModeChange(value: string): void {
    if (!this.formState) return;

    this.isGraphError = false;
    // Update the form state
    this.formState.search_legal_term = value;

    // Close document table when switching views
    this.showDocumentTable = false;
    this.emitUIStateChange({ showDocumentTable: false });
    // Close right panel when changing view mode
    this.showRightPanel = false;

    let body: GraphRequestBody;

    // In TimKiem mode, use stored seed node IDs (multiple documents from search)
    // Match the structure from buildGraphRequestBody in tim-kiem-thong-minh.component.ts
    if (this.isTimKiemMode && this.timKiemSeedNodeIds.length > 0) {
      // Build request body with multiple node IDs for TimKiem mode
      // Use fixed values matching buildGraphRequestBody: depth=1, global_limit=500, limit_per_seed=25
      // Only target_node_type changes based on view mode selection
      body = {
        co_quan_ban_hanh: [],
        depth: 1,
        global_limit: 500,
        limit_per_seed: 25,
        loai_van_ban: [],
        node_ids: this.timKiemSeedNodeIds,
        relationship_types: [],
        target_node_type: value || 'VAN_BAN',
        tinh_trang_hieu_luc: [],
        ban_hanh_year_from: null,
        ban_hanh_year_to: null,
        hieu_luc_year_from: null,
        hieu_luc_year_to: null,
      };
    } else {
      // Normal mode: use single document ID
      const targetId = this.originalDocumentEsId || this.currentDocumentEsId;

      // Validate that we have a document ID
      if (!targetId) {
        this.showErrorToast(ERROR_MESSAGES.NO_DOCUMENT_ID);
        return;
      }

      // Build default request body (no filters) - only target_node_type, depth, and limits
      body = this.formService.buildDefaultRequestBody(
        value,
        targetId,
        this.formState.depth,
        this.formState.global_limit,
        this.formState.limit_per_seed
      );
    }

    // Make the API call with default body
    this.isLoadingGraph = true;
    this.cdr.markForCheck(); // Update loading state immediately
    this.viewDetailFile.getDoThi(body).subscribe(
      (res) => {
        this.graphData = res;
        
        // In TimKiem mode, update stored seed node IDs from new response
        if (this.isTimKiemMode && res?.seed_node_ids) {
          this.timKiemSeedNodeIds = safeArray(res.seed_node_ids).map(id => String(id));
        }
        
        this.resetGraphState();
        this.viewDetailFile.graphData.next(this.graphData);
        // transformGraphData will automatically apply frontend filters if any are selected
        this.transformGraphData();
        this.showSuccessToast(SUCCESS_MESSAGES.GRAPH_UPDATED);
        this.isGraphError = false;
        this.isLoadingGraph = false;
        this.cdr.markForCheck(); // Trigger change detection after data update
      },
      (error) => {
        // Handle 404 or no data errors as usual
        this.handleApiError(error);
        this.isLoadingGraph = false;
        this.cdr.markForCheck(); // Trigger change detection after error
      }
    );
  }

  /**
   * Handle document hover from document list panel
   */
  onDocumentHover(documentId: string | null): void {
    this.hoveredDocumentId = documentId;
    // Re-render graph to apply hover effects
    this.renderGraph();
    this.cdr.markForCheck(); // Trigger change detection for OnPush strategy
  }

  /**
   * Get related documents for the current node
   * Returns list of related documents grouped by relationship type and direction
   */
  getRelatedDocuments(): Array<{
    relationshipType: string;
    relationshipLabel: string;
    direction: 'OUTGOING' | 'INCOMING';
    directionLabel: string;
    documents: Array<{
      nodeId: string;
      ten_day_du: string;
      year: string;
    }>;
  }> {
    if (!this.currentNodeId || !this.graphData) {
      return [];
    }

    const relationships = safeArray(this.graphData.relationships);
    const relatedDocsMap = new Map<string, {
      relationshipType: string;
      relationshipLabel: string;
      direction: 'OUTGOING' | 'INCOMING';
      directionLabel: string;
      documents: Array<{
        nodeId: string;
        ten_day_du: string;
        year: string;
      }>;
    }>();

    relationships.forEach((rel) => {
      const sourceId = String(rel.source_id);
      const targetId = String(rel.target_id);
      const relType = rel.loai_moi_quan_he || '';
      
      if (!relType) return;

      let relatedNodeId: string | null = null;
      let direction: 'OUTGOING' | 'INCOMING' | null = null;

      if (sourceId === this.currentNodeId) {
        relatedNodeId = targetId;
        direction = 'OUTGOING';
      } else if (targetId === this.currentNodeId) {
        relatedNodeId = sourceId;
        direction = 'INCOMING';
      }

      if (!relatedNodeId || !direction) return;

      // Get the related node
      const relatedNode = this.apiNodeMap.get(relatedNodeId);
      if (!relatedNode || relatedNode.nhan_ui !== 'Văn bản') return;

      // Skip if node is hidden
      if (this.hiddenNodeIds.has(relatedNodeId)) return;

      const relLabel = getVietnameseRelationshipLabel(relType);
      const directionLabel = direction === 'OUTGOING' ? 'TÁC ĐỘNG LÊN' : 'BỊ TÁC ĐỘNG BỞI';
      const key = `${relType}_${direction}`;

      if (!relatedDocsMap.has(key)) {
        relatedDocsMap.set(key, {
          relationshipType: relType,
          relationshipLabel: relLabel,
          direction,
          directionLabel,
          documents: [],
        });
      }

      const attrs = relatedNode.thuoc_tinh || {};
      const ten_day_du = attrs.ten_day_du || '';
      const ngay_ban_hanh = attrs.ngay_ban_hanh || '';
      const year = ngay_ban_hanh ? new Date(ngay_ban_hanh).getFullYear().toString() : '';

      relatedDocsMap.get(key)!.documents.push({
        nodeId: relatedNodeId,
        ten_day_du,
        year,
      });
    });

    // Convert map to array and sort documents by year (descending)
    return Array.from(relatedDocsMap.values()).map(group => ({
      ...group,
      documents: group.documents.sort((a, b) => {
        const yearA = parseInt(a.year) || 0;
        const yearB = parseInt(b.year) || 0;
        return yearB - yearA;
      }),
    }));
  }

  /**
   * Get total count of related documents
   */
  getRelatedDocumentsCount(): number {
    const relatedDocs = this.getRelatedDocuments();
    return relatedDocs.reduce((total, group) => total + group.documents.length, 0);
  }

  /**
   * Update document list from current graph data
   * Extracts all nodes with nhan_ui === "Văn bản" and creates document list items
   * Excludes hidden nodes
   */
  private updateDocumentList(): void {
    // Use document list service
    const result = this.documentListService.updateDocumentList(
      this.graphData,
      this.hiddenNodeIds,
      this.documentList,
      this.selectedFiles
    );

    this.documentList = result.documentList;
    this.selectedFiles = result.selectedFiles;
    
    // Invalidate caches when document list changes
    this.invalidateDocumentCaches();

    // Update "select all" state based on filtered documents
    this.updateSelectAllCheckboxState();

    if (
      this.highlightedDocumentId &&
      !this.documentList.some((doc) => doc.id === this.highlightedDocumentId)
    ) {
      this.highlightedDocumentId = null;
    }

    // Sync selectedTableRows when document list updates
    if (this.isFullTableView) {
      const paginated = this.getPaginatedDocuments();
      this.selectedTableRows = paginated.filter((item) => item.selected);
    }
  }

  showDocumentTooltip(event: MouseEvent) {
    if (!this.dataFile) return;

    let tooltipContent = '';
    tooltipContent += this.dataFile.so_hieu ? `<b>Số hiệu:</b> ${this.dataFile.so_hieu}<br>` : '';
    tooltipContent += this.dataFile.trich_yeu ? `<b>Trích yếu:</b> ${this.dataFile.trich_yeu}<br>` : '';
    tooltipContent += this.dataFile.tinh_trang_hieu_luc ? `<b>Tình trạng hiệu lực:</b> ${this.dataFile.tinh_trang_hieu_luc}<br>` : '';
    tooltipContent += this.dataFile.ngay_ban_hanh ? `<b>Ngày ban hành:</b> ${this.formatDate(this.dataFile.ngay_ban_hanh)}<br>` : '';
    tooltipContent += this.dataFile.ngay_co_hieu_luc ? `<b>Ngày có hiệu lực:</b> ${this.formatDate(this.dataFile.ngay_co_hieu_luc)}<br>` : '';
    tooltipContent += this.dataFile.co_quan_ban_hanh ? `<b>Cơ quan ban hành:</b> ${this.dataFile.co_quan_ban_hanh}<br>` : '';

    if (!tooltipContent.trim()) {
      tooltipContent = 'Không có thông tin';
    }

    // Create tooltip element
    const tooltip = this.renderer.createElement('div');
    this.renderer.addClass(tooltip, 'custom-tooltip-detail');

    const contentDiv = this.renderer.createElement('div');
    this.renderer.addClass(contentDiv, 'tooltip-content');
    this.renderer.setProperty(contentDiv, 'innerHTML', tooltipContent.trim());

    this.renderer.appendChild(tooltip, contentDiv);

    // Position and style tooltip as a card
    this.renderer.setStyle(tooltip, 'position', 'fixed');
    this.renderer.setStyle(tooltip, 'top', `${event.clientY + 10}px`);
    this.renderer.setStyle(tooltip, 'left', `${event.clientX + 10}px`);
    this.renderer.setStyle(tooltip, 'z-index', '9999');
    this.renderer.setStyle(tooltip, 'pointer-events', 'none');
    this.renderer.setStyle(tooltip, 'background-color', 'white');
    this.renderer.setStyle(tooltip, 'padding', '12px 16px');
    this.renderer.setStyle(tooltip, 'border-radius', '4px');
    this.renderer.setStyle(tooltip, 'box-shadow', '0 2px 8px rgba(0, 0, 0, 0.15)');
    this.renderer.setStyle(tooltip, 'border', '1px solid #e0e0e0');
    this.renderer.setStyle(tooltip, 'max-width', '400px');

    this.renderer.appendChild(document.body, tooltip);
    this.documentTooltipEl = tooltip;
  }

  hideDocumentTooltip() {
    if (this.documentTooltipEl) {
      this.renderer.removeChild(document.body, this.documentTooltipEl);
      this.documentTooltipEl = null;
    }
  }

  toggleRightPanel() {
    this.showRightPanel = !this.showRightPanel;
  }

  private formatDate(date: string | Date): string {
    if (!date) return '';
    const d = typeof date === 'string' ? new Date(date) : date;
    if (isNaN(d.getTime())) return date.toString();
    return d.toLocaleDateString('vi-VN', { day: '2-digit', month: '2-digit', year: 'numeric' });
  }
}
